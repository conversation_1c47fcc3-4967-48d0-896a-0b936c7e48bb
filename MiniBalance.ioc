#MicroXplorer Configuration settings - do not modify
ADC1.Channel-2\#ChannelRegularConversion=ADC_CHANNEL_1
ADC1.ContinuousConvMode=DISABLE
ADC1.EnableRegularConversion=ENABLE
ADC1.IPParameters=master,EnableRegularConversion,ContinuousConvMode,Rank-2\#ChannelRegularConversion,Channel-2\#ChannelRegularConversion,SamplingTime-2\#ChannelRegularConversion,NbrOfConversionFlag
ADC1.NbrOfConversionFlag=1
ADC1.Rank-2\#ChannelRegularConversion=1
ADC1.SamplingTime-2\#ChannelRegularConversion=ADC_SAMPLETIME_1CYCLE_5
ADC1.master=1
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.Family=STM32F1
Mcu.IP0=ADC1
Mcu.IP1=NVIC
Mcu.IP2=RCC
Mcu.IP3=SYS
Mcu.IP4=TIM2
Mcu.IP5=TIM4
Mcu.IP6=USART1
Mcu.IP7=USART3
Mcu.IPNb=8
Mcu.Name=STM32F103C(8-B)Tx
Mcu.Package=LQFP48
Mcu.Pin0=PC13-TAMPER-RTC
Mcu.Pin1=PC14-OSC32_IN
Mcu.Pin10=PB10
Mcu.Pin11=PB11
Mcu.Pin12=PB12
Mcu.Pin13=PB13
Mcu.Pin14=PB14
Mcu.Pin15=PB15
Mcu.Pin16=PA9
Mcu.Pin17=PA10
Mcu.Pin18=PA13
Mcu.Pin19=PA14
Mcu.Pin2=PC15-OSC32_OUT
Mcu.Pin20=PA15
Mcu.Pin21=PB3
Mcu.Pin22=PB4
Mcu.Pin23=PB5
Mcu.Pin24=PB8
Mcu.Pin25=PB9
Mcu.Pin26=VP_SYS_VS_Systick
Mcu.Pin27=VP_TIM2_VS_ClockSourceINT
Mcu.Pin28=VP_TIM4_VS_ClockSourceINT
Mcu.Pin3=PD0-OSC_IN
Mcu.Pin4=PD1-OSC_OUT
Mcu.Pin5=PA1
Mcu.Pin6=PA3
Mcu.Pin7=PA4
Mcu.Pin8=PA5
Mcu.Pin9=PA6
Mcu.PinsNb=29
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F103C8Tx
MxCube.Version=6.4.0
MxDb.Version=DB.6.0.40
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_2
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.SysTick_IRQn=true\:3\:0\:true\:false\:true\:false\:true
NVIC.TIM2_IRQn=true\:0\:1\:false\:false\:true\:true\:true
NVIC.USART1_IRQn=true\:1\:1\:true\:false\:true\:true\:true
NVIC.USART3_IRQn=true\:1\:1\:true\:false\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false
PA1.Signal=ADCx_IN1
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA15.GPIOParameters=GPIO_Label
PA15.GPIO_Label=OLED_DC
PA15.Locked=true
PA15.Signal=GPIO_Output
PA3.GPIOParameters=GPIO_Speed,GPIO_Label
PA3.GPIO_Label=BEEP
PA3.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA3.Locked=true
PA3.Signal=GPIO_Output
PA4.GPIOParameters=GPIO_Speed,GPIO_Label
PA4.GPIO_Label=LED
PA4.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA4.Locked=true
PA4.Signal=GPIO_Output
PA5.GPIOParameters=GPIO_PuPd,GPIO_Label
PA5.GPIO_Label=KEY_S
PA5.GPIO_PuPd=GPIO_PULLUP
PA5.Locked=true
PA5.Signal=GPIO_Input
PA6.GPIOParameters=GPIO_PuPd,GPIO_Label
PA6.GPIO_Label=KEY_J
PA6.GPIO_PuPd=GPIO_PULLUP
PA6.Locked=true
PA6.Signal=GPIO_Input
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB10.Mode=Asynchronous
PB10.Signal=USART3_TX
PB11.Mode=Asynchronous
PB11.Signal=USART3_RX
PB12.GPIOParameters=GPIO_Speed
PB12.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB12.Locked=true
PB12.Signal=GPIO_Output
PB13.GPIOParameters=GPIO_Speed
PB13.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB13.Locked=true
PB13.Signal=GPIO_Output
PB14.GPIOParameters=GPIO_Speed
PB14.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB14.Locked=true
PB14.Signal=GPIO_Output
PB15.GPIOParameters=GPIO_PuPd
PB15.GPIO_PuPd=GPIO_PULLUP
PB15.Locked=true
PB15.Signal=GPIO_Input
PB3.GPIOParameters=GPIO_Label
PB3.GPIO_Label=OLED_RTC
PB3.Locked=true
PB3.Signal=GPIO_Output
PB4.GPIOParameters=GPIO_Label
PB4.GPIO_Label=OLED_SDA
PB4.Locked=true
PB4.Signal=GPIO_Output
PB5.GPIOParameters=GPIO_Label
PB5.GPIO_Label=OLED_SCL
PB5.Locked=true
PB5.Signal=GPIO_Output
PB8.Signal=S_TIM4_CH3
PB9.Signal=S_TIM4_CH4
PC13-TAMPER-RTC.GPIOParameters=GPIO_PuPd,GPIO_Label
PC13-TAMPER-RTC.GPIO_Label=KEY_X
PC13-TAMPER-RTC.GPIO_PuPd=GPIO_PULLUP
PC13-TAMPER-RTC.Locked=true
PC13-TAMPER-RTC.Signal=GPIO_Input
PC14-OSC32_IN.GPIOParameters=GPIO_PuPd,GPIO_Label
PC14-OSC32_IN.GPIO_Label=KEY_M
PC14-OSC32_IN.GPIO_PuPd=GPIO_PULLUP
PC14-OSC32_IN.Locked=true
PC14-OSC32_IN.Signal=GPIO_Input
PC15-OSC32_OUT.GPIOParameters=GPIO_PuPd,GPIO_Label
PC15-OSC32_OUT.GPIO_Label=KEY_P
PC15-OSC32_OUT.GPIO_PuPd=GPIO_PULLUP
PC15-OSC32_OUT.Locked=true
PC15-OSC32_OUT.Signal=GPIO_Input
PD0-OSC_IN.Mode=HSE-External-Oscillator
PD0-OSC_IN.Signal=RCC_OSC_IN
PD1-OSC_OUT.Mode=HSE-External-Oscillator
PD1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F103C8Tx
ProjectManager.FirmwarePackage=STM32Cube FW_F1 V1.8.5
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=MiniBalance.ioc
ProjectManager.ProjectName=MiniBalance
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_USART1_UART_Init-USART1-false-HAL-true,4-MX_ADC1_Init-ADC1-false-HAL-true,5-MX_TIM4_Init-TIM4-false-HAL-true,6-MX_TIM2_Init-TIM2-false-HAL-true,7-MX_USART3_UART_Init-USART3-false-HAL-true
RCC.ADCFreqValue=12000000
RCC.ADCPresc=RCC_ADCPCLK2_DIV6
RCC.AHBFreq_Value=72000000
RCC.APB1CLKDivider=RCC_HCLK_DIV2
RCC.APB1Freq_Value=36000000
RCC.APB1TimFreq_Value=72000000
RCC.APB2Freq_Value=72000000
RCC.APB2TimFreq_Value=72000000
RCC.FCLKCortexFreq_Value=72000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=72000000
RCC.IPParameters=ADCFreqValue,ADCPresc,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,MCOFreq_Value,PLLCLKFreq_Value,PLLMCOFreq_Value,PLLMUL,PLLSourceVirtual,SYSCLKFreq_VALUE,SYSCLKSource,TimSysFreq_Value,USBFreq_Value,VCOOutput2Freq_Value
RCC.MCOFreq_Value=72000000
RCC.PLLCLKFreq_Value=72000000
RCC.PLLMCOFreq_Value=36000000
RCC.PLLMUL=RCC_PLL_MUL9
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.SYSCLKFreq_VALUE=72000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.TimSysFreq_Value=72000000
RCC.USBFreq_Value=72000000
RCC.VCOOutput2Freq_Value=8000000
SH.ADCx_IN1.0=ADC1_IN1,IN1
SH.ADCx_IN1.ConfNb=1
SH.S_TIM4_CH3.0=TIM4_CH3,PWM Generation3 CH3
SH.S_TIM4_CH3.ConfNb=1
SH.S_TIM4_CH4.0=TIM4_CH4,PWM Generation4 CH4
SH.S_TIM4_CH4.ConfNb=1
TIM2.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM2.IPParameters=Prescaler,Period,AutoReloadPreload
TIM2.Period=99
TIM2.Prescaler=7199
TIM4.Channel-PWM\ Generation3\ CH3=TIM_CHANNEL_3
TIM4.Channel-PWM\ Generation4\ CH4=TIM_CHANNEL_4
TIM4.IPParameters=Prescaler,Period,Channel-PWM Generation3 CH3,Channel-PWM Generation4 CH4
TIM4.Period=9999
TIM4.Prescaler=143
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART3.IPParameters=VirtualMode
USART3.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM2_VS_ClockSourceINT.Mode=Internal
VP_TIM2_VS_ClockSourceINT.Signal=TIM2_VS_ClockSourceINT
VP_TIM4_VS_ClockSourceINT.Mode=Internal
VP_TIM4_VS_ClockSourceINT.Signal=TIM4_VS_ClockSourceINT
board=custom
